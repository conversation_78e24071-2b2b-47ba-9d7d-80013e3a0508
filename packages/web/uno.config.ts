import { defineConfig, presetWind3, presetAttributify, presetTypography } from 'unocss'
import presetIcons from '@unocss/preset-icons'
import { FileSystemIconLoader } from '@iconify/utils/lib/loader/node-loaders'
import { fileURLToPath, URL } from 'node:url'

import transformerDirectives from '@unocss/transformer-directives'

export default defineConfig({
  // 扫描文件配置
  content: {
    filesystem: [
      'src/**/*.{vue,js,ts}'
    ]
  },
  presets: [
    // 基础预设 - 提供所有基础工具类（包含 Tailwind CSS 兼容的类名）
    presetWind3(),

    // 属性化预设 - 允许在 HTML 属性中使用工具类
    presetAttributify(),

    // 排版预设 - 提供排版相关的工具类
    presetTypography(),

    // 图标预设
    presetIcons({
      collections: {
        // 自定义图标集合
        u: FileSystemIconLoader(
          fileURLToPath(new URL('./src/assets/icon', import.meta.url)),
          (svg) => {
            const newSvg = svg.replace(/(<svg.*?fill=)"(?!none)(.*?)"/, '$1"currentColor"')
              .replace(/(<svg.*?stroke=)"(?!none)(.*?)"/, '$1"currentColor"')
            return newSvg;
          }
        )
      },
      // 启用常用图标集合
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      }
    })
  ],
  transformers: [
    // @apply 支持
    transformerDirectives(),
  ],
  theme: {
    breakpoints: {
      'sm': '640px',
      'md': '768px',
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
      'ut': '800px',
      'mobile': '360px'
    },
  },
  // 快捷方式
  shortcuts: {
    'btn-primary': 'bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition-colors',
    'card': 'bg-white rounded-lg shadow-lg p-6',
  }
})
