<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  LeftMenuEvents,
  type LeftMenuInstance,
  type LeftMenuProps,
  type MenuRouterItem
} from './LeftMenu'
import {useEventListener} from "@vueuse/core";

const props = withDefaults(defineProps<LeftMenuProps>(), {
  hideOperations: false,
  refreshEventListener: false,
});

const collapsed = defineModel<boolean>('collapsed', {
  default: false,
})

function changeCollapsed() {
  collapsed.value = !collapsed.value
}

const router = useRouter()
const route = useRoute();
const menuRouterList = ref<MenuRouterItem[]>([])

// 方法
function autoLoadRouter() {
  menuRouterList.value = router.getRoutes().filter(item => item.meta)
    .filter((item) => item.path.split("/").length <= 2)
    .filter((item) => item.meta.menu) as MenuRouterItem[];
}

function loadRouter() {
  if (props.loadRouter) {
    menuRouterList.value = props.loadRouter();
  } else {
    autoLoadRouter();
  }
}

onMounted(() => {
  loadRouter();
})

function go(item: MenuRouterItem) {
  router.push({ name: item.name });
}

// 获取有效的子菜单项（过滤出带有 meta.menu 的子路由）
function getValidChildren(menuItem: MenuRouterItem): MenuRouterItem[] {
  if (!menuItem.children) return [];
  return menuItem.children.filter(child => child.meta?.menu) as MenuRouterItem[];
}

if (props.refreshEventListener) {
  useEventListener(document, LeftMenuEvents.Refresh, loadRouter);
}


defineExpose<LeftMenuInstance>({
  changeCollapsed,
  refreshRouter: loadRouter
});
</script>

<template>
  <t-menu :value="route.name"
          v-model:collapsed="collapsed"
          :width="['180px', '52px']">
    <template v-if="!hideOperations"
              #operations>
      <t-button variant="text" shape="square"
                @click="changeCollapsed">
        <template #icon><t-icon name="view-list" /></template>
      </t-button>
    </template>
    <template v-if="$slots.logo" #logo>
      <slot name="logo"></slot>
    </template>

    <!-- 渲染菜单项 -->
    <template v-for="(menuItem) in menuRouterList" :key="String(menuItem.name)">
      <!-- 有子菜单的情况 -->
      <t-submenu
        v-if="getValidChildren(menuItem).length > 0"
        :value="String(menuItem.name)">
        <template #icon>
          <t-icon :class="menuItem.meta.icon"></t-icon>
        </template>
        <template #title>
          {{ menuItem.meta.title }}
        </template>

        <!-- 渲染子菜单项 -->
        <t-menu-item
          v-for="childItem in getValidChildren(menuItem)"
          :key="String(childItem.name)"
          :value="String(childItem.name)"
          @click="go(childItem)">
          <template #icon>
            <t-icon :class="childItem.meta.icon" size="small"></t-icon>
          </template>
          {{ childItem.meta.title }}
        </t-menu-item>
      </t-submenu>

      <!-- 没有子菜单的普通菜单项 -->
      <t-menu-item
        v-else
        :value="String(menuItem.name)"
        @click="go(menuItem)">
        <template #icon>
          <t-icon :class="menuItem.meta.icon"></t-icon>
        </template>
        {{ menuItem.meta.title }}
      </t-menu-item>
    </template>
  </t-menu>
</template>
