<script setup lang="ts">
import SwitchPlus from './SwitchPlus.vue';
import type { SwitchPlusProps } from './SwitchPlus'


// 定义 model
const value = defineModel<string | number | boolean>('value', {
  default: false,
});

// 定义 props (排除 modelValue)
const props = withDefaults(defineProps<SwitchPlusProps>(), {
  defaultChecked: false,
  disabled: false,
  loading: false,
  type: 'circle',
  size: 'medium',
  checkedValue: true,
  uncheckedValue: false,
  checkedColor: '#10b981'
})

</script>

<template>
  <SwitchPlus v-model:value="value"
              v-bind="props">
    <template #checked-icon>
      <span style="width: 14px; height: 14px;"
            class="i-u-checkSmall" />
    </template>
    <template #unchecked-icon>
      <span  style="width: 14px; height: 14px;"
             class="i-u-closeSmall" />
    </template>
  </SwitchPlus>
</template>
