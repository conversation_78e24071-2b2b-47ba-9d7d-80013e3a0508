<script setup lang="ts">
import { getClassPrefix } from '@utils';
import ResultForbidden from './403.vue';
import ResultNotFound from './404.vue';
import ResultServerError from './500.vue';
import type { ResultStatus, ResultSlots, ResultProps } from './Result.ts'

const RESULT_STATUS = [
  'info',
  'success',
  'warning',
  'error',
  '403',
  '404',
  '500',
  null,
] as const;

const props = withDefaults(defineProps<ResultProps>(), {
  status: 'info',
});

// 验证 status 属性
const isValidStatus = (value: any): value is ResultStatus => {
  return RESULT_STATUS.includes(value);
};

if (!isValidStatus(props.status)) {
  console.warn(`Invalid status: ${props.status}. Valid values are: ${RESULT_STATUS.join(', ')}`);
}

defineSlots<ResultSlots>();

const prefixCls = getClassPrefix('result');
</script>
<template>
  <div :class="prefixCls">
    <div
      :class="[
        `${prefixCls}-icon`,
        {
          [`${prefixCls}-icon-${status}`]: status,
          [`${prefixCls}-icon-custom`]: status === null,
        },
      ]"
    >
      <div :class="`${prefixCls}-icon-tip`">
        <slot name="icon">
          <span v-if="status === 'info'" class="i-u-info w-12 h-12" />
          <span v-else-if="status === 'success'" class="i-u-check" />
          <span v-else-if="status === 'warning'" class="i-u-info w-12 h-12" />
          <span v-else-if="status === 'error'" class="i-u-close" />
          <ResultForbidden v-else-if="status === '403'" />
          <ResultNotFound v-else-if="status === '404'" />
          <ResultServerError v-else-if="status === '500'" />
        </slot>
      </div>
    </div>
    <div v-if="title || $slots.title" :class="`${prefixCls}-title`">
      <slot name="title">
        {{ title }}
      </slot>
    </div>
    <div v-if="subtitle || $slots.subtitle" :class="`${prefixCls}-subtitle`">
      <slot name="subtitle">
        {{ subtitle }}
      </slot>
    </div>
    <div v-if="$slots.extra" :class="`${prefixCls}-extra`">
      <slot name="extra"></slot>
    </div>
    <div v-if="$slots.default" :class="`${prefixCls}-content`">
      <slot></slot>
    </div>
  </div>
</template>
