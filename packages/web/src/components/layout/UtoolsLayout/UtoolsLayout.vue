<script setup lang="ts">
import UtoolsBaseLayout from './UtoolsBaseLayout.vue'
import { ref } from 'vue'
import { LeftMenu, type LeftMenuInstance, type LoadRouterType } from '../../Menu'
import { getClassPrefix } from '@utils'
withDefaults(
  defineProps<{
    avatar?: string
    loadRouter?: LoadRouterType
    refreshEventListener?: boolean
    title?: string
  }>(),
  {
    title: 'utools demo',
    avatar: 'https://www.u-tools.cn/assets/favicon.png',
  },
)
const leftMenuRef = ref<LeftMenuInstance>()
const collapsed = ref(true)
</script>

<template>
  <div class="utools">
    <UtoolsBaseLayout>
      <template #left>
        <div :class="getClassPrefix('utools', 'left-menu')">
          <div
            class="flex items-center justify-center"
            :class="getClassPrefix('utools', 'left-menu', 'collapse')"
            @click="leftMenuRef?.changeCollapsed()"
          >
            <div
              class="w-5 h-5 font-bold"
              :class="collapsed ? 'i-u-expandLeft' : 'i-u-expandRight'"
            />
          </div>
          <LeftMenu ref="leftMenuRef"
            v-bind="{ loadRouter, refreshEventListener }"
            v-model:collapsed="collapsed"
            hideOperations
          >
            <template #logo>
              <t-avatar :image="avatar"></t-avatar>
              <div class="title">{{ title }}</div>
            </template>
          </LeftMenu>
        </div>
      </template>
      <template #default>
        <div :class="getClassPrefix('utools', 'content-wrapper')">
          <div class="flex justify-end h-full pr-3 w-full">
            <slot name="header-tips">
            </slot>
          </div>
          <div class="content">
            <div class="content-inner">
              <router-view />
            </div>
          </div>
          <slot name="footer">
            <div class="flex items-center justify-center" style="font-size: 10px">
              © {{ new Date().getFullYear() }} [xiaou]。保留所有权利
            </div>
          </slot>
        </div>
      </template>
    </UtoolsBaseLayout>
  </div>
</template>
