<script setup lang="ts">
import { SwitchPlus, SwitchPlusEnable } from '@xiaou66/u-web-ui'
import { ref } from 'vue'

const value1 = ref(false)
const value2 = ref(true)
const value3 = ref(false)
const value4 = ref(false)
const value5 = ref(false)
const value6 = ref(false)
const value7 = ref(false)
const value8 = ref(false)
const loading = ref(false)

function handleChange(value: boolean) {
  console.log('Switch changed:', value)
}

async function beforeChange(newValue: boolean) {
  console.log('Before change:', newValue)
  // 模拟异步操作
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, 1000)
  })
}
</script>

<template>
  <div class="p-6 space-y-6">
    <h1 class="text-2xl font-bold mb-4">SwitchPlus 组件演示</h1>

    <div class="space-y-4">
      <div class="flex items-center space-x-4">
        <span class="w-32">基础用法:</span>
        <SwitchPlus v-model="value1" @change="handleChange" />
        <span>当前值: {{ value1 }}</span>
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">默认选中:</span>
        <SwitchPlus v-model="value2" @change="handleChange" />
        <span>当前值: {{ value2 }}</span>
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">大尺寸 (默认):</span>
        <SwitchPlus v-model="value3" size="large" @change="handleChange" />
        <span>当前值: {{ value3 }}</span>
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">中等尺寸:</span>
        <SwitchPlus v-model="value4" size="medium" @change="handleChange" />
        <span>当前值: {{ value4 }}</span>
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">小尺寸:</span>
        <SwitchPlus v-model="value5" size="small" @change="handleChange" />
        <span>当前值: {{ value5 }}</span>
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">迷你尺寸:</span>
        <SwitchPlus v-model="value8" size="mini" @change="handleChange" />
        <span>当前值: {{ value8 }}</span>
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">圆角类型:</span>
        <SwitchPlus v-model="value6" type="round" @change="handleChange" />
        <span>当前值: {{ value6 }}</span>
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">线条类型:</span>
        <SwitchPlus v-model="value7" type="line" @change="handleChange" />
        <span>当前值: {{ value7 }}</span>
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">禁用状态:</span>
<!--        <SwitchPlus :model-value="true" disabled />-->
<!--        <SwitchPlus :model-value="false" disabled />-->
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">加载状态:</span>
        <SwitchPlus v-model="loading" loading />
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">自定义颜色:</span>
        <SwitchPlus v-model="value1" checked-color="#10b981" unchecked-color="#ef4444" />
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">带文字:</span>
        <SwitchPlus v-model="value2" checked-text="开" unchecked-text="关" />
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">带文字:</span>
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">异步切换:</span>
        <SwitchPlus v-model="value3"  />
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">带图标:</span>
        <SwitchPlus v-model="value4">
          <template #checked-icon>
            <span class="i-u-check" />
          </template>
          <template #unchecked-icon>
            <span class="i-u-close" />
          </template>
        </SwitchPlus>
      </div>

      <div class="flex items-center space-x-4">
        <span class="w-32">自定义颜色+图标:</span>
        <SwitchPlus v-model="value5" checked-color="#10b981" unchecked-color="#ef4444">
          <template #checked-icon>
            <span class="i-u-check" />
          </template>
          <template #unchecked-icon>
            <span class="i-u-close" />
          </template>
        </SwitchPlus>
        <span>图标颜色应跟随自定义颜色</span>
      </div>
      <SwitchPlusEnable size="mini" v-model:value="value5"></SwitchPlusEnable>
    </div>
  </div>
</template>

<style scoped lang="less">
.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}
</style>
